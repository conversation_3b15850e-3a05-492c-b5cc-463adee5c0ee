import React, { useState } from 'react';
import { FaMapMarkerAlt, FaCalendarAlt, FaUsers, FaUser, FaEnvelope, FaPhone, FaComment, FaPaperPlane, FaClock, FaQuestionCircle, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { FaWhatsapp } from 'react-icons/fa';
import './contact.css';

// Dropdown Arrow Component (reused from hero)
const DropdownArrow: React.FC<{ className?: string }> = ({ className = '' }) => (
  <svg
    className={className}
    style={{
      position: 'absolute',
      right: '1rem',
      top: '50%',
      transform: 'translateY(-50%)',
      width: '1rem',
      height: '1rem',
      pointerEvents: 'none',
      zIndex: 1
    }}
    fill="currentColor"
    viewBox="0 0 20 20"
  >
    <path
      fillRule="evenodd"
      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
      clipRule="evenodd"
    />
  </svg>
);

const Contact: React.FC = () => {
  // Form state - includes hero fields plus contact fields
  const [formData, setFormData] = useState({
    // Hero fields
    location: '',
    date: '',
    time: '',
    groupSize: 2,
    // Contact fields
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    eventType: '',
    specialRequests: '',
    budget: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Form submitted:', formData);
      setSubmitStatus('success');
      
      // Reset form after successful submission
      setTimeout(() => {
        setFormData({
          location: '',
          date: '',
          time: '',
          groupSize: 2,
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          eventType: '',
          specialRequests: '',
          budget: ''
        });
        setSubmitStatus('idle');
      }, 3000);
      
    } catch (error) {
      setSubmitStatus('error');
      setTimeout(() => setSubmitStatus('idle'), 3000);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="contact-section">
      <div className="contact-container">
        {/* Section Header */}
        <div className="contact-header">
          <h2 className="contact-title">Plan Your Perfect Picnic</h2>
          <p className="contact-subtitle">
            Tell us about your dream picnic experience and we'll make it happen
          </p>
        </div>

        {/* Contact Form */}
        <div className="contact-form-wrapper">
          <form onSubmit={handleSubmit} className="contact-form">
            
            {/* Personal Information Section */}
            <div className="form-section">
              <h3 className="section-title">Personal Information</h3>
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="firstName">
                    <FaUser className="input-icon" />
                    First Name *
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    required
                    placeholder="Enter your first name"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="lastName">
                    <FaUser className="input-icon" />
                    Last Name *
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    required
                    placeholder="Enter your last name"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="email">
                    <FaEnvelope className="input-icon" />
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div className="form-group">
                  <label htmlFor="phone">
                    <FaPhone className="input-icon" />
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="(*************"
                  />
                </div>
              </div>
            </div>

            {/* Event Details Section */}
            <div className="form-section">
              <h3 className="section-title">Event Details</h3>
              <div className="form-row">
                <div className="form-group" style={{ position: 'relative' }}>
                  <label htmlFor="location">
                    <FaMapMarkerAlt className="input-icon" />
                    Preferred Location *
                  </label>
                  <select
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    required
                    className="select-with-arrow"
                  >
                    <option value="">Choose Park or Location</option>
                    <option value="central-park">Central Park, New York</option>
                    <option value="griffith-park">Griffith Park, Los Angeles</option>
                    <option value="golden-gate">Golden Gate Park, San Francisco</option>
                    <option value="millennium">Millennium Park, Chicago</option>
                    <option value="boston-common">Boston Common, Boston</option>
                  </select>
                  <DropdownArrow />
                </div>

                <div className="form-group" style={{ position: 'relative' }}>
                  <label htmlFor="eventType">
                    <FaComment className="input-icon" />
                    Event Type *
                  </label>
                  <select
                    id="eventType"
                    name="eventType"
                    value={formData.eventType}
                    onChange={handleInputChange}
                    required
                    className="select-with-arrow"
                  >
                    <option value="">Select Event Type</option>
                    <option value="birthday">Birthday Celebration</option>
                    <option value="anniversary">Anniversary</option>
                    <option value="engagement">Engagement</option>
                    <option value="bridal-shower">Bridal Shower</option>
                    <option value="corporate">Corporate Event</option>
                    <option value="family-gathering">Family Gathering</option>
                    <option value="date-night">Date Night</option>
                    <option value="other">Other</option>
                  </select>
                  <DropdownArrow />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="date">
                    <FaCalendarAlt className="input-icon" />
                    Preferred Date *
                  </label>
                  <input
                    type="date"
                    id="date"
                    name="date"
                    value={formData.date}
                    onChange={handleInputChange}
                    min={new Date().toISOString().split('T')[0]}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="time">
                    <FaCalendarAlt className="input-icon" />
                    Preferred Time
                  </label>
                  <input
                    type="time"
                    id="time"
                    name="time"
                    value={formData.time}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group" style={{ position: 'relative' }}>
                  <label htmlFor="groupSize">
                    <FaUsers className="input-icon" />
                    Group Size *
                  </label>
                  <select
                    id="groupSize"
                    name="groupSize"
                    value={formData.groupSize}
                    onChange={handleInputChange}
                    required
                    className="select-with-arrow"
                  >
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 20, 25, 30].map(num => (
                      <option key={num} value={num}>
                        {num} {num === 1 ? 'Person' : 'People'}
                      </option>
                    ))}
                  </select>
                  <DropdownArrow />
                </div>

                <div className="form-group" style={{ position: 'relative' }}>
                  <label htmlFor="budget">
                    Budget Range
                  </label>
                  <select
                    id="budget"
                    name="budget"
                    value={formData.budget}
                    onChange={handleInputChange}
                    className="select-with-arrow"
                  >
                    <option value="">Select Budget Range</option>
                    <option value="under-500">Under $500</option>
                    <option value="500-1000">$500 - $1,000</option>
                    <option value="1000-2000">$1,000 - $2,000</option>
                    <option value="2000-5000">$2,000 - $5,000</option>
                    <option value="over-5000">Over $5,000</option>
                  </select>
                  <DropdownArrow />
                </div>
              </div>
            </div>

            {/* Special Requests Section */}
            <div className="form-section">
              <div className="form-group">
                <label htmlFor="specialRequests">
                  <FaComment className="input-icon" />
                  Special Requests or Additional Details
                </label>
                <textarea
                  id="specialRequests"
                  name="specialRequests"
                  value={formData.specialRequests}
                  onChange={handleInputChange}
                  rows={4}
                  placeholder="Tell us about any special requirements, dietary restrictions, themes, or other details that would help us create your perfect picnic experience..."
                />
              </div>
            </div>

            {/* Submit Button */}
            <div className="form-submit">
              <button 
                type="submit" 
                className={`submit-button ${isSubmitting ? 'submitting' : ''} ${submitStatus}`}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="spinner"></div>
                    Sending Request...
                  </>
                ) : submitStatus === 'success' ? (
                  <>
                    ✓ Request Sent Successfully!
                  </>
                ) : submitStatus === 'error' ? (
                  <>
                    ✗ Please Try Again
                  </>
                ) : (
                  <>
                    <FaPaperPlane className="button-icon" />
                    Send My Request
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
};

export default Contact;
