import React, { useState } from 'react';
import { FaChevronDown, FaChevronUp, FaQuestionCircle } from 'react-icons/fa';
// Reuse the same stylesheet

const faqData = [
  {
    id: 1,
    question: "What if it rains on the day of my booking?",
    answer: "We offer free rescheduling or indoor setup alternatives when weather doesn't cooperate. Your perfect picnic experience is guaranteed, rain or shine!"
  },
  {
    id: 2,
    question: "How far in advance should I book?",
    answer: "We recommend booking at least 3–5 days in advance, especially for weekends and special occasions. However, we'll do our best to accommodate last-minute requests when possible."
  },
  {
    id: 3,
    question: "Can I customize my picnic experience?",
    answer: "Absolutely! From romantic themes to birthday celebrations, dietary preferences to special decorations — just let us know your vision and we'll make it happen."
  },
  {
    id: 4,
    question: "Do you handle setup and cleanup?",
    answer: "Yes, our professional team takes care of everything! We arrive early to set up your perfect picnic space and return later to clean up. You just show up and enjoy every moment."
  },
  {
    id: 5,
    question: "What areas do you serve?",
    answer: "Currently, we operate throughout Greater London and surrounding areas. Planning something special outside our usual area? Reach out to us — we love making exceptions for extraordinary celebrations!"
  }
];

const FAQItem: React.FC<{ faq: (typeof faqData)[0]; index: number }> = ({ faq, index }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="faq-item">
      <button
        className="faq-question"
  onClick={() => setIsOpen(!isOpen)}
  aria-expanded={isOpen}
      
      >
        <span>{faq.question}</span>
        {isOpen ? <FaChevronUp /> : <FaChevronDown />}
      </button>
      <div className={`faq-answer ${isOpen ? 'open' : ''}`}>
        <p>{faq.answer}</p>
      </div>
    </div>
  );
};

const FAQ: React.FC = () => {
  return (
    <section className="faq-section">
      <div className="contact-container">
        <div className="faq-header">
      
          <h2 className="faq-title">Frequently Asked Questions</h2>
          <p className="faq-subtitle">
            Everything you need to know about booking the perfect picnic.
          </p>
        </div>

        <div className="faq-list">
          {faqData.map((faq, index) => (
            <FAQItem key={faq.id} faq={faq} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQ;
