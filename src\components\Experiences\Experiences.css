/* Basic Reset & Global Styles (without body styles) */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Section Container */
.lili-picnic-experiences {
  max-width: 2400px;
  width: 100%;
  padding: 5rem 1rem;
    margin: 0 auto;

}

.lili-section-container {
  background: #ffffff;
  border-radius: 24px;
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

/* Decorations */
.lili-decoration {
  position: absolute;
  font-size: 15rem;
  opacity: 0.03;
  z-index: 0;
}

.lili-decoration svg {
  width: 1em;
  height: 1em;
}

.lili-decoration-1 {
  top: -40px;
  left: -40px;
  transform: rotate(-15deg);
}

.lili-decoration-2 {
  bottom: -40px;
  right: -40px;
  transform: rotate(25deg);
}

/* Section Header */
.lili-section-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.lili-section-subtitle {
  font-family: 'Playfair Display', serif;
  font-size: 1.2rem;
  color: #2a9d8f;
  margin-bottom: 0.5rem;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.lili-section-title {
  font-family: 'Playfair Display', serif;
  font-size: 2.8rem;
  color: #264653;
  margin-bottom: 1rem;
  line-height: 1.2;
  position: relative;
  display: inline-block;
}

.lili-section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: #2a9d8f;
  border-radius: 2px;
}

.lili-section-description {
  max-width: 700px;
  margin: 0 auto;
  font-size: 1.1rem;
  color: #718096;
}

/* Experiences Grid */
.lili-experiences-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
  position: relative;
  z-index: 2;
}

/* Experience Card */
.lili-experience-card {
  background: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  border: 1px solid #eaeaea;
}

.lili-experience-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
  border-color: #2a9d8f;
}

.lili-card-header {
  position: relative;
  height: 220px;
  overflow: hidden;
}

.lili-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.lili-experience-card:hover .lili-card-image {
  transform: scale(1.05);
}

.lili-card-number {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: #2a9d8f;
  color: #ffffff;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  z-index: 2;
}

.lili-card-content {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.lili-card-title {
  font-family: 'Playfair Display', serif;
  font-size: 1.6rem;
  color: #264653;
  margin-bottom: 0.5rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.lili-card-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background: #2a9d8f;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.lili-experience-card:hover .lili-card-title::after {
  width: 100%;
}

.lili-card-location {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #2a9d8f;
  font-weight: 600;
  margin-bottom: 1rem;
}

.lili-card-location svg {
  font-size: 1.1rem;
}

/* Features Grid */
.lili-features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.lili-feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem;
  border-radius: 8px;
  background: rgba(42, 157, 143, 0.05);
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.lili-experience-card:hover .lili-feature-item {
  background: rgba(42, 157, 143, 0.1);
}

.lili-feature-icon {
  font-size: 1.2rem;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lili-feature-icon svg {
  width: 1em;
  height: 1em;
}

.lili-feature-included .lili-feature-icon {
  background: rgba(42, 157, 143, 0.15);
  color: #2a9d8f;
}

.lili-feature-not-included .lili-feature-icon {
  background: rgba(113, 128, 150, 0.1);
  color: #718096;
}

.lili-feature-label {
  font-size: 0.85rem;
  font-weight: 500;
  text-align: center;
}

.lili-feature-included .lili-feature-label {
  color: #2a9d8f;
}

.lili-feature-not-included .lili-feature-label {
  color: #718096;
}

.lili-card-description {
  color: #718096;
  margin-bottom: 1.5rem;
  flex-grow: 1;
  font-size: 1rem;
  line-height: 1.7;
}

/* Card Button */
.lili-card-button {
  background: #2a9d8f;
  color: #ffffff;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-align: center;
  text-decoration: none;
  width: 100%;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.lili-card-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: #21897a;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: -1;
}

.lili-card-button:hover::before {
  width: 100%;
}

.lili-card-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(42, 157, 143, 0.3);
}

.lili-card-button svg {
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.lili-card-button:hover svg {
  transform: translateX(5px);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .lili-section-title {
    font-size: 2.4rem;
  }

  .lili-experiences-grid {
    gap: 1.5rem;
  }
}

@media (max-width: 900px) {
  .lili-section-title {
    font-size: 2rem;
  }

  .lili-experiences-grid {
    grid-template-columns: 1fr;
    max-width: 700px;
    margin: 0 auto;
  }

  .lili-section-container {
    padding: 1.5rem;
  }
}

@media (max-width: 600px) {
  .lili-section-title {
    font-size: 1.8rem;
  }

  .lili-section-subtitle {
    font-size: 1rem;
  }

  .lili-section-description {
    font-size: 1rem;
  }

  .lili-card-title {
    font-size: 1.4rem;
  }

  .lili-card-header {
    height: 180px;
  }

  .lili-features-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 480px) {
  .lili-section-container {
    padding: 1.5rem 1rem;
  }

  .lili-features-grid {
    gap: 0.25rem;
  }
}