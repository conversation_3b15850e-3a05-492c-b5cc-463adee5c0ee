/* Professional Contact Form - Modern Design */
:root {
  --color-primary: #2A9D8F;
  --color-primary-dark: #21897a;
  --color-primary-light: #3db3a4;
  --color-dark: #264653;
  --color-light: #F8F9FA;
  --color-white: #FFFFFF;
  --color-text: #2D3748;
  --color-text-light: #718096;
  --color-border: #E2E8F0;
  --color-error: #E53E3E;
  --color-success: #38A169;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
  --border-radius: 12px;
  --transition: all 0.3s ease;
}

/* Main Section */
.contact-section {
  padding: 100px 0;
  background: linear-gradient(135deg, var(--color-light) 0%, #ffffff 100%);
  position: relative;
  overflow: hidden;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23E2E8F0" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
  z-index: 0;
}

.contact-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

/* Section Header */
.contact-header {
  text-align: center;
  margin-bottom: 60px;
}

.contact-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--color-dark);
  margin-bottom: 16px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
}

.contact-subtitle {
  font-size: 1.25rem;
  color: var(--color-text-light);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Contact Content Grid */
.contact-content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  margin-bottom: 80px;
}

/* Contact Information */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.contact-info-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 25px;
  background: var(--color-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border);
  transition: var(--transition);
}

.contact-info-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.contact-icon {
  color: var(--color-primary);
  font-size: 1.5rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.contact-info-item h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-dark);
  margin-bottom: 5px;
}

.contact-info-item p,
.contact-info-item a {
  color: var(--color-text);
  text-decoration: none;
  font-size: 1rem;
  line-height: 1.5;
}

.contact-info-item a:hover {
  color: var(--color-primary);
  text-decoration: underline;
}

/* Form Wrapper */
.contact-form-wrapper {
  background: var(--color-white);
  border-radius: 20px;
  padding: 40px;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-border);
  position: relative;
  overflow: hidden;
}

.contact-form-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
}

.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-dark);
  margin-bottom: 30px;
  text-align: center;
}

/* Form Sections */
.form-section {
  margin-bottom: 40px;
}

.form-section:last-of-type {
  margin-bottom: 30px;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-dark);
  margin-bottom: 24px;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--color-border);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: var(--color-primary);
}

/* Form Layout */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Labels */
.form-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--color-dark);
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.input-icon {
  color: var(--color-primary);
  font-size: 0.9rem;
}

/* Input Styles */
.form-group input,
.form-group select,
.form-group textarea {
  padding: 16px 20px;
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius);
  font-size: 1rem;
  color: var(--color-text);
  background: var(--color-white);
  transition: var(--transition);
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(42, 157, 143, 0.1);
  transform: translateY(-1px);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--color-text-light);
}

/* Select Dropdown */
.select-with-arrow {
  appearance: none;
  background-image: none;
  padding-right: 50px;
  cursor: pointer;
}

/* Textarea */
.form-group textarea {
  resize: vertical;
  min-height: 120px;
  line-height: 1.6;
}

/* Submit Button */
.form-submit {
  text-align: center;
  margin-top: 40px;
}

.submit-button {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  color: var(--color-white);
  border: none;
  padding: 18px 40px;
  border-radius: var(--border-radius);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: 10px;
  min-width: 200px;
  justify-content: center;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(42, 157, 143, 0.3);
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:active {
  transform: translateY(0);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Button States */
.submit-button.submitting {
  background: var(--color-text-light);
  cursor: not-allowed;
}

.submit-button.success {
  background: var(--color-success);
}

.submit-button.error {
  background: var(--color-error);
}

/* Loading Spinner */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid var(--color-white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.button-icon {
  font-size: 0.9rem;
}

/* FAQ Section - Enhanced with Primary Background */
.faq-section {
  margin-top: 80px;
  padding: 80px 0;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  position: relative;
  overflow: hidden;
}

.faq-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="faq-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23faq-pattern)"/></svg>');
  opacity: 0.3;
}

.faq-section .contact-container {
  position: relative;
  z-index: 1;
}

.faq-header {
  text-align: center;
  margin-bottom: 60px;
}

.faq-icon {
  color: var(--color-white);
  font-size: 3rem;
  margin-bottom: 20px;
  display: block;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.faq-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-white);
  text-align: center;
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  letter-spacing: -0.02em;
}

.faq-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.faq-list {
  max-width: 900px;
  margin: 0 auto;
  display: grid;
  gap: 20px;
}

.faq-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.4s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.faq-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
}

.faq-question {
  width: 100%;
  padding: 25px 30px;
  background: none;
  border: none;
  text-align: left;
  font-size: 1.15rem;
  font-weight: 600;
  color: var(--color-dark);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
}

.faq-question::before {
 
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--color-primary);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.faq-question:hover::before,
.faq-item:hover .faq-question::before {
  transform: scaleY(1);
}

.faq-question:hover {
  color: var(--color-primary);
  padding-left: 35px;
}

.faq-question svg {
  color: var(--color-primary);
  font-size: 1.1rem;
  transition: all 0.3s ease;
  background: rgba(42, 157, 143, 0.1);
  padding: 8px;
  border-radius: 50%;
  width: 32px;
  height: 32px;
}

.faq-question[aria-expanded="true"] svg {
  transform: rotate(180deg);
  background: var(--color-primary);
  color: white;
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: all 0.4s ease;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.faq-answer.open {
  max-height: 300px;
}

.faq-answer p {
  padding: 25px 30px 30px;
  margin: 0;
  color: var(--color-text);
  line-height: 1.7;
  font-size: 1rem;
  border-top: 1px solid rgba(42, 157, 143, 0.1);
}

/* FAQ Number Badge */
.faq-question::after {
 
  position: absolute;
  right: 60px;
  top: 50%;
  transform: translateY(-50%);
  background: var(--color-primary);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 700;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.faq-question:hover::after {
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-section {
    padding: 60px 0;
  }

  .contact-title {
    font-size: 2.2rem;
  }

  .contact-subtitle {
    font-size: 1.1rem;
  }

  .contact-content-grid {
    grid-template-columns: 1fr;
    gap: 40px;
    margin-bottom: 60px;
  }

  .contact-form-wrapper {
    padding: 30px 20px;
    border-radius: 16px;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 14px 16px;
  }

  .submit-button {
    width: 100%;
    padding: 16px 30px;
  }

  .faq-section {
    padding: 60px 0;
    margin-top: 60px;
  }

  .faq-icon {
    font-size: 2.2rem;
    margin-bottom: 15px;
  }

  .faq-title {
    font-size: 2rem;
  }

  .faq-subtitle {
    font-size: 1.1rem;
  }

  .faq-question {
    padding: 20px 25px;
    font-size: 1rem;
  }

  .faq-question:hover {
    padding-left: 30px;
  }

  .faq-question::after {
    right: 50px;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
  }

  .faq-answer p {
    padding: 20px 25px 25px;
  }
}

@media (max-width: 480px) {
  .contact-container {
    padding: 0 15px;
  }

  .contact-title {
    font-size: 1.8rem;
  }

  .contact-form-wrapper {
    padding: 25px 15px;
  }

  .contact-info-item {
    padding: 20px;
    gap: 15px;
  }

  .contact-icon {
    font-size: 1.3rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 12px 14px;
    font-size: 0.95rem;
  }

  .faq-section {
    padding: 50px 0;
    margin-top: 50px;
  }

  .faq-header {
    margin-bottom: 40px;
  }

  .faq-icon {
    font-size: 2rem;
    margin-bottom: 12px;
  }

  .faq-title {
    font-size: 1.8rem;
  }

  .faq-subtitle {
    font-size: 1rem;
  }

  .faq-question {
    padding: 18px 20px;
    font-size: 0.95rem;
  }

  .faq-question:hover {
    padding-left: 25px;
  }

  .faq-question::after {
    right: 45px;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
  }

  .faq-answer p {
    padding: 18px 20px 22px;
    font-size: 0.9rem;
  }
}
