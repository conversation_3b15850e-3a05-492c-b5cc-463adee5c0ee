/* src/components/Header/Header.css */
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

:root {
  /* Premium color palette */
  --color-primary: #2A9D8F;        /* Sophisticated teal */
  --color-primary-dark: #21897a;   /* Darker teal */
  --color-secondary: #E9C46A;      /* Warm gold */
  --color-dark: #264653;           /* Deep blue-green */
  --color-light: #F8F9FA;          /* Light background */
  --color-white: #FFFFFF;          
  --color-text: #2D3748;           /* Dark gray for text */
  --color-text-light: #718096;     /* Lighter gray */
  --color-overlay: rgba(38, 70, 83, 0.85); /* Overlay color */
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;
  
  /* Typography */
  --font-heading: 'Playfair Display', serif;
  --font-body: 'Plus Jakarta Sans', sans-serif;
  
  /* Borders */
  --border-radius: 8px;
  --border-radius-lg: 16px;
  
  /* Shadows */
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 6px 15px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 12px 25px rgba(0, 0, 0, 0.15);
  
  /* Transitions */
  --transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

/* Top Bar Styles */
.top-bar {
  background-color: var(--color-dark);
  color: var(--color-white);
  padding: var(--spacing-sm) 0;
  font-size: 0.9rem;
  font-family: var(--font-body);
  transition: var(--transition);
  z-index: 1001;
  position: relative;
}

.top-bar.scrolled {
  transform: translateY(-100%);
}

.top-bar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 5%;
}

.contact-info {
  display: flex;
  gap: var(--spacing-lg);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  transition: var(--transition);
}

.contact-item:hover {
  color: var(--color-secondary);
}

.contact-icon {
  font-size: 0.9rem;
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
}

.social-links a {
  color: var(--color-white);
  text-decoration: none;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.social-links a:hover {
  color: var(--color-secondary);
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.social-icon {
  font-size: 0.9rem;
}

/* Main Header Styles */
.main-header {
  background-color: var(--color-white);
  padding: var(--spacing-md) 0;
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: var(--transition);
}

.main-header.scrolled {
  box-shadow: var(--shadow-md);
  padding: var(--spacing-sm) 0;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 5%;
  gap: var(--spacing-md);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-decoration: none;
  font-size: 1.8rem;
  font-weight: 700;
  font-family: var(--font-heading);
  color: var(--color-dark);
  transition: var(--transition);
  flex-shrink: 0;
}

.logo:hover {
  color: var(--color-primary);
}

.logo-icon {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.logo:hover .logo-icon {
  background-color: var(--color-primary-dark);
  transform: rotate(-5deg);
}

.logo-icon svg {
  width: 24px;
  height: 24px;
}

.desktop-nav ul {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
  margin: 0;
  padding: 0;
}

.desktop-nav ul li a {
  color: var(--color-text);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
  position: relative;
  padding: var(--spacing-sm) 0;
  font-family: var(--font-body);
  font-size: 1.05rem;
  letter-spacing: 0.5px;
}

.desktop-nav ul li a:hover {
  color: var(--color-primary);
}

.desktop-nav ul li a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: var(--transition);
  border-radius: 2px;
}

.desktop-nav ul li a:hover::after {
  width: 100%;
}

/* Tablet Navigation */
.tablet-nav {
  display: none;
}

.tablet-nav ul {
  display: flex;
  list-style: none;
  gap: var(--spacing-md);
  margin: 0;
  padding: 0;
}

.tablet-nav ul li a {
  color: var(--color-text);
  text-decoration: none;
  font-weight: 500;
  font-family: var(--font-body);
  font-size: 0.95rem;
  transition: var(--transition);
  padding: var(--spacing-sm) 0;
}

.tablet-nav ul li a:hover {
  color: var(--color-primary);
}

.book-btn {
  background: linear-gradient(135deg, var(--color-primary));
  color: var(--color-white);
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 1rem;
  font-weight: 600;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-family: var(--font-body);
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.book-btn:hover {
  background: linear-gradient(135deg, var(--color-primary-dark), var(--color-primary));
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.btn-icon {
  font-size: 1rem;
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  width: 40px;
  height: 40px;
  padding: 0;
  z-index: 1003;
  color: var(--color-dark);
  font-size: 1.5rem;
  transition: var(--transition);
}

.mobile-menu-btn:hover {
  color: var(--color-primary);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.mobile-menu-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* Mobile Menu */
.mobile-menu {
  position: fixed;
  top: 0;
  right: -100%;
  height: 100vh;
  width: 320px;
  max-width: 90%;
  background-color: var(--color-white);
  box-shadow: var(--shadow-lg);
  transition: var(--transition);
  z-index: 1002;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.mobile-menu.open {
  right: 0;
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.mobile-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 1.4rem;
  font-weight: 700;
  font-family: var(--font-heading);
  color: var(--color-dark);
}

.mobile-menu-close {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.5rem;
  color: var(--color-text);
  transition: var(--transition);
}

.mobile-menu-close:hover {
  color: var(--color-primary);
  transform: rotate(90deg);
}

.mobile-nav {
  padding: var(--spacing-lg);
  flex-grow: 1;
}

.mobile-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav ul li {
  margin-bottom: var(--spacing-md);
}

.mobile-nav ul li a {
  display: block;
  padding: var(--spacing-sm) 0;
  color: var(--color-text);
  text-decoration: none;
  font-family: var(--font-body);
  font-size: 1.2rem;
  font-weight: 500;
  transition: var(--transition);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.mobile-nav ul li a:hover {
  color: var(--color-primary);
  padding-left: var(--spacing-sm);
}

.mobile-cta {
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.mobile-book-btn {
  width: 100%;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  color: var(--color-white);
  border: none;
  padding: var(--spacing-md);
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-family: var(--font-body);
}

.mobile-book-btn:hover {
  background: linear-gradient(135deg, var(--color-primary-dark), var(--color-primary));
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.mobile-contact {
  padding: var(--spacing-lg);
  background-color: var(--color-light);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.mobile-contact .contact-item {
  margin-bottom: var(--spacing-md);
  color: var(--color-text);
}

.mobile-contact .contact-item:hover {
  color: var(--color-primary);
}

.mobile-social-links {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.mobile-social-links a {
  color: var(--color-text);
  text-decoration: none;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--color-white);
  box-shadow: var(--shadow-sm);
}

.mobile-social-links a:hover {
  color: var(--color-primary);
  transform: translateY(-3px);
}

/* Tablet Responsive Styles (769px to 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .top-bar-content {
    padding: 0 4%;
  }
  
  .header-container {
    padding: 0 4%;
    gap: var(--spacing-sm);
  }
  
  .desktop-nav {
    display: none;
  }
  
  .tablet-nav {
    display: block;
    flex-grow: 1;
    margin: 0 var(--spacing-md);
  }
  
  .logo {
    font-size: 1.6rem;
  }
  
  .logo-icon {
    width: 40px;
    height: 40px;
  }
  
  .book-btn {
    padding: var(--spacing-sm) var(--spacing-sm);
    font-size: 0.95rem;
  }
  
  .book-btn span {
    display: none;
  }
  
  .book-btn .btn-icon {
    font-size: 1.1rem;
    margin: 0;
  }
  
  .mobile-menu-btn {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Mobile Responsive Styles (up to 768px) */
@media (max-width: 768px) {
  .top-bar-content {
    padding: 0 4%;
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .contact-info {
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-md);
  }
  
  .header-container {
    padding: 0 4%;
    gap: var(--spacing-sm);
  }
  
  .desktop-nav, .tablet-nav {
    display: none;
  }
  
  .logo {
    font-size: 1.5rem;
  }
  
  .logo-icon {
    width: 38px;
    height: 38px;
  }
  
  .book-btn span {
    display: none;
  }
  
  .book-btn .btn-icon {
    font-size: 1.1rem;
    margin: 0;
  }
  
  .mobile-menu-btn {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Small Mobile Devices (up to 480px) */
@media (max-width: 480px) {
  .logo {
    font-size: 1.4rem;
  }
  
  .logo-icon {
    width: 36px;
    height: 36px;
  }
  
  .book-btn {
    padding: 10px;
  }
}