/* --- Fonts & Root Variables --- */
@import url('https://fonts.googleapis.com/css2?family=Clash+Display:wght@600&family=Inter:wght@400;500;600&display=swap');

:root {
  --font-display: 'Clash Display', sans-serif;
  --font-body: 'Inter', sans-serif;

  --color-primary: #2A9D8F;
  --color-primary-dark: #21897a;
  --color-dark: #264653;
  --color-light: #F8F9FA;
  --color-white: #FFFFFF;
  --color-text: #2D3748;
  --color-text-light: #718096;

  --shadow-md: 0 4px 12px rgba(38, 70, 83, 0.08);
  --shadow-lg: 0 10px 25px rgba(38, 70, 83, 0.15);
  --border-radius: 16px;
  --transition-smooth: all 0.3s ease-in-out;
}

/* --- Base Layout --- */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-light);
  font-family: var(--font-body);
  color: var(--color-text);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* --- Section Wrapper --- */
.picnic-section {
  max-width: 1200px;
  margin: 4rem auto;
  padding: 2rem;
  background: transparent;
}

/* --- Header --- */
.picnic-header {
  text-align: center;
  margin-bottom: 4rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.picnic-title {
  font-family: var(--font-display);
  font-size: 3.5rem;
  color: var(--color-dark);
  margin-bottom: 1rem;
}

.picnic-subtitle {
  font-size: 1.1rem;
  color: var(--color-text-light);
  max-width: 600px;
  margin: 0 auto;
}

/* --- Grid Layout --- */
.picnic-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  align-items: stretch;
}

/* --- Card Design --- */
.picnic-card {
  background-color: var(--color-white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  transition: var(--transition-smooth);
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* --- Card Content --- */
.card-content {
  padding: 2.5rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  height: 100%;
}

.card-icon {
  font-size: 4rem;
  line-height: 1;
  margin-bottom: 1.5rem;
  transition: var(--transition-smooth);
}

.card-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--color-dark);
  margin-bottom: 0.75rem;
  transition: color var(--transition-smooth);
}

.card-description {
  color: var(--color-text-light);
  font-size: 0.95rem;
  margin-bottom: 1.5rem;
  transition: color var(--transition-smooth);
  flex-grow: 1;
}

/* --- Features --- */
.card-features-list {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
  display: inline-flex;
  flex-direction: column;
  gap: 0.75rem;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.75rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--color-text-light);
  transition: color var(--transition-smooth);
}

.feature-icon {
  width: 20px;
  height: 20px;
  color: var(--color-primary);
  flex-shrink: 0;
  transition: color var(--transition-smooth);
}

/* --- Button --- */
.card-button {
  display: inline-block;
  background-color: var(--color-primary);
  color: var(--color-white);
  padding: 0.8rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  margin-top: auto;
  border: 2px solid var(--color-primary);
  transition: var(--transition-smooth);
}

/* --- Hover Effects --- */
.picnic-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
  background-color: var(--color-primary);
}

.picnic-card:hover .card-icon {
  transform: scale(1.1);
}

.picnic-card:hover .card-title,
.picnic-card:hover .card-description,
.picnic-card:hover .feature-item,
.picnic-card:hover .feature-icon {
  color: var(--color-white);
}

.picnic-card:hover .card-button {
  background-color: var(--color-white);
  color: var(--color-primary);
  border-color: var(--color-white);
}

/* --- Responsive Adjustments --- */
@media (max-width: 768px) {
  .picnic-title {
    font-size: 2.8rem;
  }
}
