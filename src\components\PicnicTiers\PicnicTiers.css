/* --- Root Variables --- */
:root {
  --font-display: 'Clash Display', sans-serif;
  --font-body: 'Inter', sans-serif;

  --color-primary:#2A9D8F ;
  --color-primary-dark: #414A4C;
  --color-dark: #264653;
  --color-light: #F8F9FA;
  --color-white: #FFFFFF;
  --color-text: #2D3748;
  --color-text-light: #718096;
}

/* --- Section Styling --- */
.ppp-picnic-tiers-section-revised {
  position: relative;
  z-index: 1;
  padding: 100px 20px;
  background-color: var(--color-light);
  font-family: var(--font-body);
  color: var(--color-text);
  text-align: center;
}

.ppp-container-revised {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  position: relative;
  z-index: 2;
}

/* --- Headings --- */
.ppp-headline-revised {
  font-family: var(--font-display);
  font-size: 3.2em;
  color: var(--color-dark);
  margin-bottom: 15px;
  line-height: 1.2;
  font-weight: 700;
}

.ppp-subheadline-revised {
  font-family: var(--font-body);
  font-size: 1.4em;
  color: var(--color-text-light);
  margin-bottom: 70px;
  max-width: 750px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  font-weight: 400;
}

/* --- Tiers Grid --- */
.ppp-tiers-grid-revised {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 35px;
  margin-bottom: 70px;
}

/* --- Tier Card Styling --- */
.ppp-tier-card-revised {
  background-color: var(--color-white);
  padding: 35px;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease, filter 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.ppp-tier-card-revised:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  filter: drop-shadow(0 0 15px var(--color-primary)) drop-shadow(0 0 30px rgba(0, 255, 255, 0.2));
}

.ppp-tier-header-revised {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.ppp-tier-icon-revised {
  font-size: 3.5em;
  color: var(--color-primary);
  margin-bottom: 15px;
  line-height: 1;
}

.ppp-tier-title-revised {
  font-family: var(--font-display);
  font-size: 1.8em;
  color: var(--color-dark);
  margin: 0;
}

.ppp-tier-description-revised {
  font-size: 1.1em;
  color: var(--color-text);
  line-height: 1.6;
  max-width: 250px;
  margin: 0 auto;
}

/* --- CTA Button --- */
.ppp-cta-container-revised {
  margin-top: 50px;
}

.ppp-cta-button-revised {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 18px 35px;
  background-color: var(--color-primary);
  color: var(--color-white);
  font-family: var(--font-body);
  font-size: 1.2em;
  font-weight: 600;
  border: none;
  border-radius: 50px;
  text-decoration: none;
  transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
  cursor: pointer;
  box-shadow: 0 8px 15px rgba(0, 255, 255, 0.3);
}

.ppp-cta-button-revised:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 12px 20px rgba(0, 255, 255, 0.4);
}

/* --- Responsive Adjustments --- */

/* Tablets */
@media (max-width: 992px) {
  .ppp-headline-revised {
    font-size: 2.8em;
  }
  .ppp-subheadline-revised {
    font-size: 1.2em;
    margin-bottom: 50px;
  }
  .ppp-tiers-grid-revised {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
  .ppp-tier-card-revised {
    padding: 30px;
  }
  .ppp-tier-icon-revised {
    font-size: 3em;
  }
  .ppp-tier-title-revised {
    font-size: 1.6em;
  }
  .ppp-tier-description-revised {
    font-size: 1em;
  }
  .ppp-cta-button-revised {
    padding: 16px 30px;
    font-size: 1.1em;
  }
}

/* Mobile */
@media (max-width: 768px) {
  .ppp-picnic-tiers-section-revised {
    padding: 80px 15px;
  }
  .ppp-headline-revised {
    font-size: 2.4em;
  }
  .ppp-subheadline-revised {
    font-size: 1.1em;
    margin-bottom: 40px;
  }
  .ppp-tiers-grid-revised {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  .ppp-tier-card-revised {
    padding: 25px;
  }
  .ppp-tier-icon-revised {
    font-size: 2.8em;
  }
  .ppp-tier-title-revised {
    font-size: 1.4em;
  }
  .ppp-tier-description-revised {
    font-size: 0.95em;
  }
  .ppp-cta-button-revised {
    padding: 14px 28px;
    font-size: 1em;
  }
}

/* Small mobile screens */
@media (max-width: 576px) {
  .ppp-headline-revised {
    font-size: 2em;
  }
  .ppp-subheadline-revised {
    font-size: 1em;
  }
  .ppp-tiers-grid-revised {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  .ppp-tier-icon-revised {
    font-size: 2.5em;
  }
  .ppp-tier-title-revised {
    font-size: 1.3em;
  }
  .ppp-cta-button-revised {
    font-size: 0.95em;
    padding: 12px 22px;
  }
}
