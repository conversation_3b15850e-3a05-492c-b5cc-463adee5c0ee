/* ===== NEWSLETTER SUBSCRIPTION SECTION ===== */
.newsletter-subscription {
  position: relative;
  z-index: 10;
  margin-bottom: -80px; /* Overlap with footer */
}

.newsletter-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.newsletter-content {
  background: linear-gradient(135deg, #F5C100 0%, #f7d633 100%);
  border-radius: 20px;
  padding: 60px 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.newsletter-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/><circle cx="50" cy="10" r="0.5" fill="%23000" opacity="0.03"/><circle cx="20" cy="80" r="0.5" fill="%23000" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.newsletter-text {
  flex: 1;
  margin-right: 40px;
  position: relative;
  z-index: 2;
}

.newsletter-text h2 {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1a1a1a;
  margin-bottom: 15px;
  line-height: 1.2;
  font-family: 'Inter', sans-serif;
}

.newsletter-text p {
  font-size: 1.1rem;
  color: #2d2d2d;
  line-height: 1.6;
  font-weight: 500;
  opacity: 0.9;
}

.newsletter-form-container {
  flex: 0 0 400px;
  position: relative;
  z-index: 2;
}

.newsletter-form {
  width: 100%;
}

.input-group {
  display: flex;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.input-group:focus-within {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.newsletter-input {
  flex: 1;
  padding: 18px 20px;
  border: none;
  outline: none;
  font-size: 1rem;
  color: #333;
  background: transparent;
  font-family: 'Inter', sans-serif;
}

.newsletter-input::placeholder {
  color: #999;
  font-weight: 400;
}

.newsletter-input:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.newsletter-button {
  background: linear-gradient(135deg, #1a1a1a 0%, #444 100%);
  border: none;
  padding: 18px 24px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  position: relative;
  overflow: hidden;
}

.newsletter-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.newsletter-button:hover::before {
  left: 100%;
}

.newsletter-button:hover {
  background: linear-gradient(135deg, #F5C100 0%, #f7d633 100%);
  color: #1a1a1a;
  transform: scale(1.05);
}

.newsletter-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.success-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background: white;
  padding: 18px 20px;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  color: #10b981;
  font-weight: 600;
  font-size: 1rem;
}

.success-icon {
  width: 24px;
  height: 24px;
  background: #10b981;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 992px) {
  .newsletter-content {
    flex-direction: column;
    text-align: center;
    padding: 50px 40px;
  }

  .newsletter-text {
    margin-right: 0;
    margin-bottom: 30px;
  }

  .newsletter-text h2 {
    font-size: 2.2rem;
  }

  .newsletter-form-container {
    flex: none;
    width: 100%;
    max-width: 400px;
  }
}

@media (max-width: 768px) {
  .newsletter-subscription {
    margin-bottom: -60px;
  }

  .newsletter-content {
    padding: 40px 30px;
    border-radius: 16px;
  }

  .newsletter-text h2 {
    font-size: 1.8rem;
  }

  .newsletter-text p {
    font-size: 1rem;
  }

  .newsletter-form-container {
    max-width: 100%;
  }

  .newsletter-input {
    padding: 16px 18px;
    font-size: 0.95rem;
  }

  .newsletter-button {
    padding: 16px 20px;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .newsletter-container {
    padding: 0 15px;
  }

  .newsletter-content {
    padding: 30px 20px;
    border-radius: 12px;
  }

  .newsletter-text h2 {
    font-size: 1.6rem;
  }

  .newsletter-text p {
    font-size: 0.95rem;
  }

  .input-group {
    border-radius: 10px;
  }

  .newsletter-input {
    padding: 14px 16px;
    font-size: 0.9rem;
  }

  .newsletter-button {
    padding: 14px 18px;
    font-size: 1rem;
  }
}
