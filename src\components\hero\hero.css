@import url('https://fonts.googleapis.com/css2?family=Clash+Display:wght@600;700&family=Inter:wght@400;500;600;700&display=swap');

/* --- Hero Section --- */
.hero {
  position: relative;
  height: 90vh;
  min-height: 700px;
  display: flex;
  align-items: center;
  padding: 2rem;
  overflow: hidden;
}

.hero-bg-slider,
.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.hero-bg {
  background-size: cover;
  background-position: center;
  opacity: 0;
  transition: opacity 1.5s ease-in-out;
}

.hero-bg.active {
  opacity: 1;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(120deg, rgba(38, 70, 83, 0.85), rgba(42, 157, 143, 0.65));
  z-index: 1;
}
select option {
  background-color: #264653; /* dark background */
  color: #ffffff; /* white text */
}
.form-group select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3rem;
}

.hero-text {
  text-align: center;
  max-width: 900px;
  padding: 0 1rem;
  animation: fadeInUp 1s ease-out;
}

.hero-text h1 {
  font-family: 'Clash Display', sans-serif;
  font-size: 3.5rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  letter-spacing: -0.5px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.hero-text p {
  font-family: 'Inter', sans-serif;
  font-size: 1.4rem;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.92);
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* --- CTA Button --- */
.cta-button {
  background: linear-gradient(135deg, #2A9D8F, #21897a);
  color: white;
  border: none;
  padding: 1.1rem 2.8rem;
  font-size: 1.2rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  font-family: 'Inter', sans-serif;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 0.7rem;
}

.cta-button:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #21897a, #2A9D8F);
}

/* --- Glassmorphic Search Box --- */
.search-box {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 18px;
  padding: 2.5rem;
  width: 100%;
  max-width: 1100px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  animation: fadeIn 1.2s ease-out 0.3s both;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* --- Form Layout --- */
.form-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.7rem;
  font-size: 1.05rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.input-icon {
  color: #ffffff;
  font-size: 1.2rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
}

.form-group input,
.form-group select {
  padding: 1.05rem 1.3rem;
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 12px;
  font-family: 'Inter', sans-serif;
  font-size: 1.05rem;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  transition: all 0.3s ease;
  appearance: none;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.15);
}

.form-group input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

/* Custom dropdown arrow */
.form-group select {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1rem;
  color: #ffffff;
}

/* Focus styles */
.form-group select:focus,
.form-group input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
}

/* --- Submit Button --- */
.submit-button {
  background: rgba(38, 70, 83, 0.85);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 1.4rem 2rem; /* Increased padding for size */
  font-size: 1rem;    /* Slightly larger font size */
  font-weight: 600;
  border-radius: 14px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  font-family: 'Inter', sans-serif;
  letter-spacing: 0.4px;
  align-self: flex-end;
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.25);
  height: auto; /* Let height adjust naturally */
  min-height: 60px; /* Ensures it's taller */
}

.submit-button:hover {
  background: rgba(30, 60, 70, 0.95);
  transform: translateY(-4px);
  box-shadow: 0 10px 24px rgba(0, 0, 0, 0.4);
}

/* --- Animations --- */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* --- Responsive Design --- */
@media (max-width: 1024px) {
  .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .submit-button {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  .hero {
    height: auto;
    padding: 6rem 1.5rem 3rem;
  }

  .hero-text h1 {
    font-size: 2.5rem;
  }

  .hero-text p {
    font-size: 1.2rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .submit-button {
    grid-column: span 1;
  }

  .search-box {
    padding: 1.8rem;
  }

  .datetime-group {
    grid-template-columns: 1fr;
    color: #ffffff;
  }
}

@media (max-width: 480px) {
  .hero-text h1 {
    font-size: 2rem;
  }

  .hero-text p {
    font-size: 1.1rem;
  }

  .cta-button {
    width: 100%;
    justify-content: center;
    padding: 1rem;
  }

  .form-group select,
  .form-group input {
    padding: 0.9rem 1.1rem;
  }
}
input[type="date"] {
  appearance: none;
  -webkit-appearance: none;
  background-color: transparent;
  color: #fff;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 1rem;
  height: 56px; /* default size on desktop */
  border: 1px solid rgba(255, 255, 255, 0.3);
  width: 100%;
  box-sizing: border-box;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(1); /* makes the icon white */
  opacity: 0.8;
  cursor: pointer;
}

@media (max-width: 768px) {
  input[type="date"] {
    height: 48px; /* smaller but consistent on mobile */
    font-size: 1rem;
    padding: 0.5rem 1rem;
  }
}
