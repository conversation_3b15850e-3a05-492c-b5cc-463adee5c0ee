/* Professional Footer - Premium Design */
:root {
  --color-primary: #2A9D8F;
  --color-primary-dark: #21897a;
  --color-primary-light: #3db3a4;
  --color-dark: #264653;
  --color-light: #F8F9FA;
  --color-white: #FFFFFF;
  --color-text: #2D3748;
  --color-text-light: #718096;
  --color-border: #E2E8F0;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
  --border-radius: 12px;
  --transition: all 0.3s ease;
}

/* Main Footer */
.footer {
  background: linear-gradient(135deg, var(--color-dark) 0%, #1a3d47 100%);
  color: var(--color-white);
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footer-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="rgba(42,157,143,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23footer-pattern)"/></svg>');
  opacity: 0.5;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
  margin-top: 30px;
}

/* Main Footer Content */
.footer-main {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 50px;
  padding: 80px 0 60px;
}

.footer-section h4.footer-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--color-white);
  margin-bottom: 25px;
  position: relative;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background: var(--color-primary);
  border-radius: 2px;
}

/* Company Info Section */
.footer-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.logo-icon {
  color: var(--color-primary);
  font-size: 2rem;
}

.footer-logo h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--color-white);
  margin: 0;
}

.footer-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.7;
  margin-bottom: 25px;
  font-size: 1rem;
}

.footer-rating {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stars {
  display: flex;
  gap: 3px;
}

.star {
  color: #FFD700;
  font-size: 1rem;
}

.footer-rating span {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: 0.9rem;
}

/* Links */
.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 1rem;
  transition: var(--transition);
  position: relative;
}

.footer-links a:hover {
  color: var(--color-primary);
  padding-left: 8px;
}

.footer-links a::before {
  content: '';
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 2px;
  background: var(--color-primary);
  transition: width 0.3s ease;
}

.footer-links a:hover::before {
  width: 8px;
}

/* Contact Info */
.footer-contact {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.contact-icon {
  color: var(--color-primary);
  font-size: 1.1rem;
  width: 20px;
  flex-shrink: 0;
}

.contact-item a,
.contact-item span {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.95rem;
  transition: var(--transition);
}

.contact-item a:hover {
  color: var(--color-primary);
}

/* Follow Us Section - Professional Social Links */
.follow-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 25px;
  font-size: 1rem;
}

.social-links-main {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.social-links-main {
  display: grid;
  grid-template-columns: repeat(2, 40px);
  gap: 12px;
}

/* Make each icon a square box */
.social-link {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.08);
  border: 2px solid rgba(255, 255, 255, 0.1);
  color: var(--color-white);
  transition: var(--transition);
  position: relative;
}

/* Hide text span */
.social-link span {
  display: none;
}

/* Adjust hover effects to preserve color highlight */
.social-link:hover {
  transform: scale(1.1);
  background: rgba(42, 157, 143, 0.2);
  border-color: var(--color-primary);
}

/* Social-specific hover colors (still works) */
.social-link.facebook:hover {
  border-color: #1877F2;
  background: rgba(24, 119, 242, 0.15);
}
.social-link.facebook:hover svg {
  color: #1877F2;
}

.social-link.instagram:hover {
  border-color: #E4405F;
  background: rgba(228, 64, 95, 0.15);
}
.social-link.instagram:hover svg {
  color: #E4405F;
}

.social-link.twitter:hover {
  border-color: #1DA1F2;
  background: rgba(29, 161, 242, 0.15);
}
.social-link.twitter:hover svg {
  color: #1DA1F2;
}

.social-link.linkedin:hover {
  border-color: #0A66C2;
  background: rgba(10, 102, 194, 0.15);
}
.social-link.linkedin:hover svg {
  color: #0A66C2;
}


/* Footer Bottom */
.footer-bottom {
  padding: 30px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.copyright p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-size: 0.9rem;
}

.footer-links-bottom {
  display: flex;
  gap: 30px;
}

.footer-links-bottom a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-size: 0.9rem;
  transition: var(--transition);
}

.footer-links-bottom a:hover {
  color: var(--color-primary);
}

.made-with-love {
  display: flex;
  align-items: center;
  gap: 5px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.heart-icon {
  color: #E53E3E;
  animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer-main {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 60px 0 40px;
  }

  .social-links-main {
    gap: 10px;
  }

  .social-link {
    padding: 10px 14px;
    font-size: 0.95rem;
  }

  .social-link svg {
    font-size: 1.1rem;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .footer-links-bottom {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .footer-container {
    padding: 0 15px;
  }

  .footer-main {
    padding: 50px 0 30px;
    gap: 30px;
  }

  .footer-logo h3 {
    font-size: 1.5rem;
  }

  .footer-description,
  .follow-description {
    font-size: 0.9rem;
  }

  .social-links-main {
    gap: 8px;
  }

  .social-link {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  .social-link svg {
    font-size: 1rem;
  }

  .footer-links-bottom {
    flex-direction: column;
    gap: 10px;
  }
}
