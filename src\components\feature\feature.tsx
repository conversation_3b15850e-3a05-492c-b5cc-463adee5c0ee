import React from 'react';
import './feature.css';

// Updated type definition without image properties
type PicnicPackage = {
  id: number;
  icon: string;
  title: string;
  description: string;
  features: string[];
};

// Data for the picnic packages
const picnicData: PicnicPackage[] = [
  {
    id: 1,
    icon: '🌸',
    title: 'Luxury Date Picnic',
    description: 'A romantic experience for couples with elegant decor and premium dining setup.',
    features: ['Private location setup', 'Floral arrangements', 'Candlelight décor'],
  },
  {
    id: 2,
    icon: '❤️',
    title: 'Romantic Setup',
    description: 'A romantic setup for couples with elegant decor and premium dining setup.',
    features: ['Private location setup', 'Floral arrangements', 'Candlelight décor'],
  },
  {
    id: 3,
    icon: '🎂',
    title: 'Birthday Picnic',
    description: 'Celebrate your birthday outdoors with a fully customized theme and setup.',
    features: ['Birthday decorations', 'Custom cake options', 'Party games'],
  },
  {
    id: 4,
    icon: '💍',
    title: 'Proposal Setup',
    description: 'Create a memorable proposal with a romantic picnic setup tailored to your love story.',
    features: ['Personalized decor', 'Candlelight ambiance', 'Photographer available'],
  },
  {
    id: 5,
    icon: '🏢',
    title: 'Corporate Picnic',
    description: 'Enhance team bonding with a relaxing outdoor picnic designed for corporate groups.',
    features: ['Team-building activities', 'Catering options', 'Relaxation zones'],
  },
  {
    id: 6,
    icon: '🎉',
    title: 'Kids Party Pavilion',
    description: 'A fun and vibrant picnic setup designed specifically for kids\' parties.',
    features: ['Themed decorations', 'Games and activities', 'Kid-friendly menu'],
  },
 
];


const PicnicPackages: React.FC = () => {
  return (
    <section className="picnic-section">
      <div className="picnic-header">
        <h2 className="picnic-title">Curated Picnic Experiences</h2>
        <p className="picnic-subtitle">
          Select your ideal occasion, and we'll craft the perfect alfresco moment. We handle everything from setup to cleanup.
        </p>
      </div>

      <div className="picnic-grid">
        {picnicData.map((pkg) => (
          <div key={pkg.id} className="picnic-card">
            <div className="card-content">
              <div className="card-icon">{pkg.icon}</div>
              <h3 className="card-title">{pkg.title}</h3>
              <p className="card-description">{pkg.description}</p>
              <ul className="card-features-list">
                {pkg.features.map((feature, index) => (
                  <li key={index} className="feature-item">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="feature-icon">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
              <a href="#" className="card-button">
                Explore Package
              </a>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default PicnicPackages;