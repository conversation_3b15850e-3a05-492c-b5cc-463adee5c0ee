import React, { useState, useEffect } from 'react';
import './review.css';

// New testimonials data based on user requirements
const testimonials = [
  {
    id: 1,
    quote: "From the decor to the service, everything was absolutely flawless. It felt like a dream come true.",
    author: "<PERSON>",
    occasion: "Birthday Picnic",
    rating: 5
  },
  {
    id: 2,
    quote: "We booked a sunset experience and it exceeded every expectation. Elegant, peaceful, and perfectly arranged.",
    author: "<PERSON> & <PERSON>",
    occasion: "Engagement Celebration",
    rating: 5
  },
  {
    id: 3,
    quote: "An unforgettable experience. The attention to detail and the ambiance made it feel so luxurious.",
    author: "<PERSON>",
    occasion: "Bridal Shower",
    rating: 5
  },
  {
    id: 4,
    quote: "Beautiful setting, excellent hospitality, and just the right touch of romance. We'll definitely be back!",
    author: "<PERSON>",
    occasion: "Date Night Picnic",
    rating: 5
  },
  {
    id: 5,
    quote: "I surprised my wife with a picnic for our anniversary. She was blown away. The team truly knows how to create magic.",
    author: "<PERSON>",
    occasion: "Anniversary Picnic",
    rating: 5
  }
];

const StarRating: React.FC<{ rating: number }> = ({ rating }) => {
  return (
    <div className="star-rating">
      {[...Array(rating)].map((_, index) => (
        <span key={index} className="star">★</span>
      ))}
    </div>
  );
};

const Testimonials: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      handleNext();
    }, 5000);

    return () => clearInterval(interval);
  }, [currentIndex]);

  const handleNext = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setTimeout(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
      setIsAnimating(false);
    }, 300);
  };

  const handlePrev = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setTimeout(() => {
      setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
      setIsAnimating(false);
    }, 300);
  };

  const goToSlide = (index: number) => {
    if (isAnimating || index === currentIndex) return;
    setIsAnimating(true);
    setTimeout(() => {
      setCurrentIndex(index);
      setIsAnimating(false);
    }, 300);
  };

  const current = testimonials[currentIndex];

  return (
    <section className="testimonials-section">
      <div className="testimonials-container">
        {/* Section Header */}
        <div className="testimonials-header">
          <h2 className="testimonials-title">Our Happy Clients</h2>
          <p className="testimonials-subtitle">Real words from those who've experienced the magic.</p>
        </div>

        {/* Testimonial Slider */}
        <div className="testimonial-slider">
          <button 
            className="slider-btn slider-btn-prev" 
            onClick={handlePrev}
            aria-label="Previous testimonial"
          >
            ‹
          </button>

          <div className="testimonial-content">
            <div className={`testimonial-card ${isAnimating ? 'fade-out' : 'fade-in'}`}>
              <StarRating rating={current.rating} />
              
              <blockquote className="testimonial-quote">
                "{current.quote}"
              </blockquote>
              
              <div className="testimonial-author">
                <span className="author-name">— {current.author}</span>
                <span className="author-occasion">{current.occasion}</span>
              </div>
            </div>
          </div>

          <button 
            className="slider-btn slider-btn-next" 
            onClick={handleNext}
            aria-label="Next testimonial"
          >
            ›
          </button>
        </div>

        {/* Dots Navigation */}
        <div className="testimonial-dots">
          {testimonials.map((_, index) => (
            <button
              key={index}
              className={`dot ${index === currentIndex ? 'active' : ''}`}
              onClick={() => goToSlide(index)}
              aria-label={`Go to testimonial ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
