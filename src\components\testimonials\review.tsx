import React, { useState, useEffect } from 'react';
import './review.css';

// Professional testimonials data with high-quality images
const testimonials = [
  {
    id: 1,
    quote: "The picnic experience exceeded all our expectations. The attention to detail and professional service made our corporate event truly memorable. Our team bonding has never been stronger.",
    author: "<PERSON>",
    title: "CO Founder",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80"
  },
  {
    id: 2,
    quote: "Outstanding service and exceptional quality. The picnic setup was flawless, and every detail was perfectly executed. Our clients were thoroughly impressed with the professional presentation.",
    author: "<PERSON>",
    title: "CTO",
    avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80"
  },
  {
    id: 3,
    quote: "The implementation was seamless, and the support team was incredibly responsive. We've seen a 40% increase in team satisfaction since incorporating these professional picnic experiences.",
    author: "Michael Rodriguez",
    title: "CEO",
    avatar: "https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80"
  },
  {
    id: 4,
    quote: "This service has completely revolutionized our client entertainment approach. The professional presentation and seamless execution have made a significant impact on our business relationships.",
    author: "Emma Wilson",
    title: "Director",
    avatar: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80"
  }
];

// Professional hero image for testimonials section
const testimonialHeroImage = "https://images.unsplash.com/photo-1530587191325-3db32d826c18?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80";

const Testimonial = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      handleSlideChange((currentIndex + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [currentIndex]);

  const handleSlideChange = (index: number) => {
    if (currentIndex === index) return;

    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentIndex(index);
      setTimeout(() => {
        setIsTransitioning(false);
      }, 50);
    }, 300);
  };

  const current = testimonials[currentIndex];

  return (
    <section className="testimonial-section">
      <div className="testimonial-container">
        <div className="testimonial-image-container">
          <div className="testimonial-image-bg"></div>
          <img
            src={testimonialHeroImage}
            alt="Professional picnic service experience"
            className="testimonial-image"
            loading="lazy"
          />
        </div>
        
        <div className="testimonial-content">
          <div className={`testimonial-slide ${isTransitioning ? 'fade-out' : 'fade-in'}`}>
            <div className="stars-container">
              {[...Array(5)].map((_, i) => (
                <svg key={i} className="star" viewBox="0 0 24 24">
                  <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                </svg>
              ))}
            </div>
            
            <p className="testimonial-quote">
              {current.quote}
            </p>
            
            <div className="testimonial-divider"></div>
            
            <div className="testimonial-author-container">
              <img 
                src={current.avatar} 
                alt={current.author} 
                className="testimonial-author-avatar" 
              />
              
              <div className="testimonial-author-info">
                <span className="testimonial-author-name">{current.author}</span>
                <span className="testimonial-author-title">{current.title}</span>
              </div>
            </div>
          </div>
          
          <div className="testimonial-dots">
            {testimonials.map((_, index) => (
              <button 
                key={index}
                className={`testimonial-dot ${index === currentIndex ? 'active' : ''}`}
                onClick={() => handleSlideChange(index)}
                aria-label={`View testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonial;