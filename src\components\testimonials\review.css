/* Modern Testimonials Section - Professional Design */
:root {
  --color-primary: #2A9D8F;
  --color-primary-dark: #21897a;
  --color-dark: #264653;
  --color-light: #F8F9FA;
  --color-white: #FFFFFF;
  --color-text: #2D3748;
  --color-text-light: #718096;
  --color-gold: #FFD700;
  --color-gold-dark: #FFC107;
}

/* Main Section - No gaps */
.testimonials-section {
  padding: 80px 0;
  margin: 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
  display: block;
  width: 100%;
}

.testimonials-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Section Header */
.testimonials-header {
  text-align: center;
  margin-bottom: 60px;
}

.testimonials-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--color-dark);
  margin-bottom: 16px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
}

.testimonials-subtitle {
  font-size: 1.25rem;
  color: var(--color-text-light);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  font-weight: 400;
}

/* Testimonial Slider */
.testimonial-slider {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
}

.testimonial-content {
  max-width: 800px;
  width: 100%;
  margin: 0 60px;
}

.testimonial-card {
  background: var(--color-white);
  border-radius: 24px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(42, 157, 143, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.testimonial-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
}

/* Star Rating */
.star-rating {
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  gap: 4px;
}

.star {
  color: var(--color-gold);
  font-size: 1.5rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.star:hover {
  transform: scale(1.1);
}

/* Quote */
.testimonial-quote {
  font-size: 1.5rem;
  line-height: 1.7;
  color: var(--color-text);
  font-style: italic;
  margin-bottom: 40px;
  font-weight: 400;
  position: relative;
}

.testimonial-quote::before,
.testimonial-quote::after {
  content: '"';
  font-size: 2rem;
  color: var(--color-primary);
  font-weight: 700;
  position: absolute;
  opacity: 0.3;
}

.testimonial-quote::before {
  top: -10px;
  left: -20px;
}

.testimonial-quote::after {
  bottom: -30px;
  right: -20px;
}

/* Author */
.testimonial-author {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.author-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-dark);
  letter-spacing: 0.5px;
}

.author-occasion {
  font-size: 0.95rem;
  color: var(--color-text-light);
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Slider Buttons */
.slider-btn {
  background: var(--color-white);
  border: 2px solid var(--color-primary);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--color-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(42, 157, 143, 0.2);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.slider-btn:hover {
  background: var(--color-primary);
  color: var(--color-white);
  transform: translateY(-50%) scale(1.05);
}

.slider-btn-prev {
  left: 0;
}

.slider-btn-next {
  right: 0;
}

/* Dots Navigation */
.testimonial-dots {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(42, 157, 143, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: var(--color-primary);
  transform: scale(1.2);
}

.dot:hover {
  background: var(--color-primary-dark);
  transform: scale(1.1);
}

/* Animations */
.fade-in {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease;
}

.fade-out {
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
  .testimonials-container {
    padding: 60px 15px;
  }
  
  .testimonials-title {
    font-size: 2.2rem;
  }
  
  .testimonials-subtitle {
    font-size: 1.1rem;
  }
  
  .testimonial-content {
    margin: 0 20px;
  }
  
  .testimonial-card {
    padding: 40px 25px;
  }
  
  .testimonial-quote {
    font-size: 1.2rem;
  }
  
  .slider-btn {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .slider-btn-prev {
    left: -10px;
  }
  
  .slider-btn-next {
    right: -10px;
  }
}

@media (max-width: 480px) {
  .testimonials-container {
    padding: 40px 10px;
  }
  
  .testimonials-title {
    font-size: 1.8rem;
  }
  
  .testimonial-content {
    margin: 0 10px;
  }
  
  .testimonial-card {
    padding: 30px 20px;
  }
  
  .testimonial-quote {
    font-size: 1.1rem;
  }
  
  .slider-btn {
    display: none;
  }
}
