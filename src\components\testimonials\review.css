:root {
  --color-primary: #2A9D8F;
  --color-primary-dark: #21897a;
  --color-dark: #264653;
  --color-light: #F8F9FA;
  --color-white: #FFFFFF;
  --color-text: #2D3748;
  --color-text-light: #718096;
}

.testimonial-section {
  padding: 80px 0;
  font-family: 'Inter', sans-serif;
  background-color: var(--color-white);
}

.testimonial-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  gap: 60px;
}

.testimonial-image-container {
  position: relative;
  width: 230px;
  height: 230px;
  flex-shrink: 0;
}

.testimonial-image-bg {
  width: 100%;
  height: 100%;
  background-color: var(--color-light);
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
  border: 3px solid var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary), 0 0 0 6px rgba(42, 157, 143, 0.3);
}

.testimonial-image {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
}

.testimonial-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.stars-container {
  display: flex;
  margin-bottom: 20px;
}

.star {
  width: 24px;
  height: 24px;
  fill: #FFD700;
  margin-right: 4px;
}

.testimonial-slide {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.testimonial-slide.fade-out {
  opacity: 0;
  transform: translateX(-20px);
}

.testimonial-slide.fade-in {
  opacity: 1;
  transform: translateX(0);
}

.testimonial-quote {
  font-size: 20px;
  line-height: 1.6;
  color: var(--color-text);
  margin-bottom: 20px;
  max-width: 600px;
  font-weight: 400;
  position: relative;
  padding-left: 30px;
}

.testimonial-quote::before {
  content: "";
  position: absolute;
  left: 0;
  top: -15px;
  font-size: 60px;
  color: var(--color-primary);
  font-family: Georgia, serif;
  line-height: 1;
}

.testimonial-divider {
  height: 1px;
  background-color: #E5E7EB;
  margin: 20px 0;
  width: 100%;
  max-width: 500px;
}

.testimonial-author-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.testimonial-author-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 16px;
  border: 2px solid var(--color-primary);
}

.testimonial-author-info {
  display: flex;
  flex-direction: column;
}

.testimonial-author-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-dark);
}

.testimonial-author-title {
  font-size: 14px;
  color: var(--color-text-light);
}

.testimonial-dots {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
}

.testimonial-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #D1D5DB;
  border: none;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.testimonial-dot.active {
  width: 10px;
  height: 10px;
  background-color: var(--color-primary);
}

@media (max-width: 768px) {
  .testimonial-container {
    flex-direction: column;
    text-align: center;
    gap: 30px;
    padding: 0 30px;
  }
  
  .testimonial-image-container {
    width: 200px;
    height: 200px;
    margin: 0 auto;
  }
  
  .testimonial-image {
    width: 100%;
    height: 100%;
  }
  
  .testimonial-quote {
    font-size: 16px;
    margin: 20px auto;
    padding: 0 10px 0 30px;
    max-width: 100%;
  }
  
  .testimonial-quote::before {
    left: 10px;
  }
  
  .testimonial-divider {
    margin: 20px auto;
  }
  
  .testimonial-author-container {
    justify-content: center;
  }
  
  .testimonial-dots {
    justify-content: center;
    margin-top: 20px;
  }
}

@media (min-width: 769px) and (max-width: 992px) {
  .testimonial-container {
    gap: 40px;
    padding: 0 30px;
  }
  
  .testimonial-image-container {
    width: 180px;
    height: 180px;
  }
  
  .testimonial-quote {
    font-size: 18px;
    max-width: 100%;
  }
}