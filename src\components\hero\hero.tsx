import React, { useState, useEffect } from 'react';
import { FaCalendarAlt, FaMapMarkerAlt, FaUsers } from 'react-icons/fa';
import './hero.css';

const Hero: React.FC = () => {
  const [location, setLocation] = useState('');
  const [date, setDate] = useState('');
  const [time, setTime] = useState('');
  const [groupSize, setGroupSize] = useState(2);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const images = [
    'https://images.unsplash.com/photo-1501555088652-021faa106b9b?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8b3V0ZG9vcnxlbnwwfHwwfHx8MA%3D%3D',
    'https://media.istockphoto.com/id/1307333424/photo/happy-asian-family-in-the-garden-they-are-having-fun-playing-and-blowing-bubbles.webp?a=1&b=1&s=612x612&w=0&k=20&c=_BhAxG3mDoTDIYfCnFMByOUg95pM4Bfk2JwAZ3eMC5c=',
    'https://images.unsplash.com/photo-1477512076069-d31eb021716f?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8N3x8ZmFtaWx5JTIwcGljbmljfGVufDB8fDB8fHww'
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, [images.length]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log({ location, date, time, groupSize });
  };

  return (
    <section className="hero">
      {/* Background slider */}
      <div className="hero-bg-slider">
        {images.map((img, index) => (
          <div 
            key={index}
            className={`hero-bg ${index === currentImageIndex ? 'active' : ''}`}
            style={{ backgroundImage: `url(${img})` }}
          />
        ))}
      </div>
      
      <div className="hero-overlay"></div>
      
      <div className="hero-content">
        <div className="hero-text">
          <h1>Book a Magical Picnic Experience in Seconds</h1>
          <p>From a simple table for two to a full luxury setup — decorated table, catering, live music, and even a private photographer.</p>
          <button className="cta-button">
            ✨ Search Available Spots
          </button>
        </div>

        <div className="search-box">
          <form onSubmit={handleSubmit}>
            <div className="form-grid">
              {/* Where? */}
              <div className="form-group">
                <label htmlFor="location">
                  <FaMapMarkerAlt className="input-icon" />
                  Where?
                </label>
                <select 
                  id="location" 
                  value={location} 
                  onChange={(e) => setLocation(e.target.value)}
                  required
                >
                  <option value="">Choose Park or Location</option>
                  <option value="central-park">Central Park, New York</option>
                  <option value="griffith-park">Griffith Park, Los Angeles</option>
                  <option value="golden-gate">Golden Gate Park, San Francisco</option>
                  <option value="millennium">Millennium Park, Chicago</option>
                  <option value="boston-common">Boston Common, Boston</option>
                </select>
              </div>

              {/* When? */}
              <div className="form-group">
                <label htmlFor="date">
                  <FaCalendarAlt className="input-icon" />
                  When?
                </label>
                <div className="datetime-group">
                  <input 
                    type="date" 
                    id="date" 
                    value={date} 
                    onChange={(e) => setDate(e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    required
                  />
                  
                </div>
              </div>

              {/* Group Size */}
              <div className="form-group">
                <label htmlFor="groupSize">
                  <FaUsers className="input-icon" />
                  Group Size
                </label>
                <select 
                  id="groupSize" 
                  value={groupSize} 
                  onChange={(e) => setGroupSize(parseInt(e.target.value))}
                  required
                >
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                    <option key={num} value={num}>{num} {num === 1 ? 'Person' : 'People'}</option>
                  ))}
                </select>
              </div>

              <button type="submit" className="submit-button">
                Check Availability
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
};

export default Hero;