import React, { useEffect, useRef } from 'react';
import './Experiences.css'; // Assuming you save the CSS in this file

// Import React Icons
import { FaMapMarkerAlt, FaCheck, FaTimes, FaArrowRight, FaShoppingBasket } from 'react-icons/fa';
import { LuTreePalm } from 'react-icons/lu'; // For the tree decoration

const PicnicExperiences: React.FC = () => {
  const cardsRef = useRef<HTMLDivElement[]>([]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry, index) => {
          if (entry.isIntersecting) {
            setTimeout(() => {
              (entry.target as HTMLElement).style.opacity = '1';
              (entry.target as HTMLElement).style.transform = 'translateY(0)';
            }, 200 * index);
          }
        });
      },
      { threshold: 0.1 }
    );

    cardsRef.current.forEach((card) => {
      if (card) {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease, box-shadow 0.4s ease';
        observer.observe(card);
      }
    });

    return () => {
      cardsRef.current.forEach((card) => {
        if (card) {
          observer.unobserve(card);
        }
      });
    };
  }, []);

  return (
    <section className="lili-picnic-experiences">
      <div className="lili-section-container">
        {/* Background Decorations */}
        <div className="lili-decoration lili-decoration-1">
          <FaShoppingBasket />
        </div>
        <div className="lili-decoration lili-decoration-2">
          <LuTreePalm />
        </div>

        <div className="lili-section-header">
          <div className="lili-section-subtitle">Premium Experiences</div>
          <h2 className="lili-section-title">Exclusive Picnic Settings</h2>
          <p className="lili-section-description">
            Curated experiences in stunning locations with premium amenities for unforgettable moments.
          </p>
        </div>

        <div className="lili-experiences-grid">
          {/* Romantic Lake Picnic */}
          <div className="lili-experience-card" ref={(el) => (cardsRef.current[0] = el!)}>
            <div className="lili-card-header">
              <img
                src="https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80"
                alt="Romantic Lake Picnic"
                className="lili-card-image"
              />
              <div className="lili-card-number">1</div>
            </div>
            <div className="lili-card-content">
              <h3 className="lili-card-title">Sunset Romance by the Lake</h3>
              <div className="lili-card-location">
                <FaMapMarkerAlt />
                <span>Zilker Park, Austin</span>
              </div>

              <div className="lili-features-grid">
                <div className="lili-feature-item lili-feature-included">
                  <div className="lili-feature-icon">
                    <FaCheck />
                  </div>
                  <div className="lili-feature-label">Setup</div>
                </div>
                <div className="lili-feature-item lili-feature-included">
                  <div className="lili-feature-icon">
                    <FaCheck />
                  </div>
                  <div className="lili-feature-label">Catering</div>
                </div>
                <div className="lili-feature-item lili-feature-not-included">
                  <div className="lili-feature-icon">
                    <FaTimes />
                  </div>
                  <div className="lili-feature-label">Music</div>
                </div>
              </div>

              <p className="lili-card-description">
                An intimate lakeside setting with a beautifully decorated table, perfect for proposals or anniversary
                celebrations as the sun sets over the water.
              </p>
              <button className="lili-card-button">
                Reserve Experience <FaArrowRight />
              </button>
            </div>
          </div>

          {/* Family Picnic */}
          <div className="lili-experience-card" ref={(el) => (cardsRef.current[1] = el!)}>
            <div className="lili-card-header">
              <img
                src="https://images.unsplash.com/photo-1540747913346-19e32dc3e97e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1205&q=80"
                alt="Family Picnic"
                className="lili-card-image"
              />
              <div className="lili-card-number">2</div>
            </div>
            <div className="lili-card-content">
              <h3 className="lili-card-title">Family Pavilion & Games</h3>
              <div className="lili-card-location">
                <FaMapMarkerAlt />
                <span>Prospect Park, Brooklyn</span>
              </div>

              <div className="lili-features-grid">
                <div className="lili-feature-item lili-feature-included">
                  <div className="lili-feature-icon">
                    <FaCheck />
                  </div>
                  <div className="lili-feature-label">Setup</div>
                </div>
                <div className="lili-feature-item lili-feature-not-included">
                  <div className="lili-feature-icon">
                    <FaTimes />
                  </div>
                  <div className="lili-feature-label">Catering</div>
                </div>
                <div className="lili-feature-item lili-feature-included">
                  <div className="lili-feature-icon">
                    <FaCheck />
                  </div>
                  <div className="lili-feature-label">Music</div>
                </div>
              </div>

              <p className="lili-card-description">
                A spacious pavilion with comfortable seating and classic lawn games, creating the perfect environment
                for family bonding and memorable gatherings.
              </p>
              <button className="lili-card-button">
                Reserve Experience <FaArrowRight />
              </button>
            </div>
          </div>

          {/* Corporate Picnic */}
          <div className="lili-experience-card" ref={(el) => (cardsRef.current[2] = el!)}>
            <div className="lili-card-header">
              <img
                src="https://images.unsplash.com/photo-1505373877841-8d25f7d46678?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1112&q=80"
                alt="Corporate Picnic"
                className="lili-card-image"
              />
              <div className="lili-card-number">3</div>
            </div>
            <div className="lili-card-content">
              <h3 className="lili-card-title">Corporate Retreat Oasis</h3>
              <div className="lili-card-location">
                <FaMapMarkerAlt />
                <span>Golden Gate Park, San Francisco</span>
              </div>

              <div className="lili-features-grid">
                <div className="lili-feature-item lili-feature-included">
                  <div className="lili-feature-icon">
                    <FaCheck />
                  </div>
                  <div className="lili-feature-label">Setup</div>
                </div>
                <div className="lili-feature-item lili-feature-included">
                  <div className="lili-feature-icon">
                    <FaCheck />
                  </div>
                  <div className="lili-feature-label">Catering</div>
                </div>
                <div className="lili-feature-item lili-feature-included">
                  <div className="lili-feature-icon">
                    <FaCheck />
                  </div>
                  <div className="lili-feature-label">Music</div>
                </div>
              </div>

              <p className="lili-card-description">
                Professional setup designed for team building with premium catering and ambient acoustic music to
                facilitate connection in a natural setting.
              </p>
              <button className="lili-card-button">
                Reserve Experience <FaArrowRight />
              </button>
            </div>
          </div>

          {/* Kids' Party Picnic */}
          <div className="lili-experience-card" ref={(el) => (cardsRef.current[3] = el!)}>
            <div className="lili-card-header">
              <img
                src="https://images.unsplash.com/photo-1570303345338-e1f0ed7b40a3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
                alt="Kids' Party Picnic"
                className="lili-card-image"
              />
              <div className="lili-card-number">4</div>
            </div>
            <div className="lili-card-content">
              <h3 className="lili-card-title">Children's Wonderland</h3>
              <div className="lili-card-location">
                <FaMapMarkerAlt />
                <span>Millennium Park, Chicago</span>
              </div>

              <div className="lili-features-grid">
                <div className="lili-feature-item lili-feature-included">
                  <div className="lili-feature-icon">
                    <FaCheck />
                  </div>
                  <div className="lili-feature-label">Setup</div>
                </div>
                <div className="lili-feature-item lili-feature-included">
                  <div className="lili-feature-icon">
                    <FaCheck />
                  </div>
                  <div className="lili-feature-label">Catering</div>
                </div>
                <div className="lili-feature-item lili-feature-not-included">
                  <div className="lili-feature-icon">
                    <FaTimes />
                  </div>
                  <div className="lili-feature-label">Music</div>
                </div>
              </div>

              <p className="lili-card-description">
                A vibrant, playful space with colorful decor, kid-sized tables, and delicious treats designed to
                delight children at birthdays and celebrations.
              </p>
              <button className="lili-card-button">
                Reserve Experience <FaArrowRight />
              </button>
            </div>
          </div>

          {/* Proposal Picnic */}
          <div className="lili-experience-card" ref={(el) => (cardsRef.current[4] = el!)}>
            <div className="lili-card-header">
              <img
                src="https://images.unsplash.com/photo-1532712938310-34cb3982ef74?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
                alt="Proposal Picnic"
                className="lili-card-image"
              />
              <div className="lili-card-number">5</div>
            </div>
            <div className="lili-card-content">
              <h3 className="lili-card-title">Luxury Proposal Package</h3>
              <div className="lili-card-location">
                <FaMapMarkerAlt />
                <span>Griffith Park, Los Angeles</span>
              </div>

              <div className="lili-features-grid">
                <div className="lili-feature-item lili-feature-included">
                  <div className="lili-feature-icon">
                    <FaCheck />
                  </div>
                  <div className="lili-feature-label">Setup</div>
                </div>
                <div className="lili-feature-item lili-feature-included">
                  <div className="lili-feature-icon">
                    <FaCheck />
                  </div>
                  <div className="lili-feature-label">Catering</div>
                </div>
                <div className="lili-feature-item lili-feature-included">
                  <div className="lili-feature-icon">
                    <FaCheck />
                  </div>
                  <div className="lili-feature-label">Music</div>
                </div>
              </div>

              <p className="lili-card-description">
                An unforgettable floral arrangement with sunset views and professional photography to capture your
                special moment in perfect detail.
              </p>
              <button className="lili-card-button">
                Reserve Experience <FaArrowRight />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PicnicExperiences;