import React from 'react';
import { FaEnvelope, FaPhone, FaMapMarkerAlt, FaClock, FaFacebookF, FaInstagram, FaTwitter, FaLinkedinIn, FaHeart, FaLeaf, FaStar, FaWhatsapp } from 'react-icons/fa';
import './footer.css';
import NewsletterSubscription from '../News/news';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <>
    <NewsletterSubscription/>
    <footer className="footer">
      <div className="footer-container">
        
        {/* Main Footer Content */}
        <div className="footer-main">
          
          {/* Company Info */}
          <div className="footer-section">
            <div className="footer-logo">
              <FaLeaf className="logo-icon" />
              <h3>PicnicBliss</h3>
            </div>
            <p className="footer-description">
              Creating magical outdoor experiences across Greater London. From intimate date nights to grand celebrations, we craft unforgettable picnic moments that bring people together.
            </p>
            <div className="footer-rating">
              <div className="stars">
                {[1, 2, 3, 4, 5].map(star => (
                  <FaStar key={star} className="star" />
                ))}
              </div>
              <span>5.0 • 200+ Happy Customers</span>
            </div>
          </div>

          {/* Quick Links */}
          <div className="footer-section">
            <h4 className="footer-title">Quick Links</h4>
            <ul className="footer-links">
              <li><a href="#hero">Home</a></li>
              <li><a href="#packages">Packages</a></li>
              <li><a href="#tiers">Pricing</a></li>
              <li><a href="#testimonials">Reviews</a></li>
              <li><a href="#contact">Contact</a></li>
              <li><a href="#faq">FAQ</a></li>
            </ul>
          </div>

          {/* Follow Us Section */}
          <div className="footer-section">
            <h4 className="footer-title">Follow Us</h4>
            <p className="follow-description">
              Stay connected for picnic inspiration, tips, and exclusive offers
            </p>
            <div className="social-links-main">
              <a href="#" className="social-link facebook" aria-label="Facebook">
                <FaFacebookF />
                <span>Facebook</span>
              </a>
              <a href="#" className="social-link instagram" aria-label="Instagram">
                <FaInstagram />
                <span>Instagram</span>
              </a>
              <a href="#" className="social-link twitter" aria-label="Twitter">
                <FaTwitter />
                <span>Twitter</span>
              </a>
              <a href="#" className="social-link linkedin" aria-label="LinkedIn">
                <FaLinkedinIn />
                <span>LinkedIn</span>
              </a>
            </div>
          </div>

          {/* Contact Info */}
          <div className="footer-section">
            <h4 className="footer-title">Get in Touch</h4>
            <div className="footer-contact">
              <div className="contact-item">
                <FaEnvelope className="contact-icon" />
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </div>
              <div className="contact-item">
                <FaWhatsapp className="contact-icon" />
                <a href="tel:+447123456789">+44 7123 456789</a>
              </div>
              <div className="contact-item">
                <FaMapMarkerAlt className="contact-icon" />
                <span>Greater London & nearby areas</span>
              </div>
              <div className="contact-item">
                <FaClock className="contact-icon" />
                <span>Mon–Sat | 9:00 AM – 7:00 PM</span>
              </div>
            </div>
          </div>
        </div>

      

        {/* Footer Bottom */}
        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <div className="copyright">
              <p>© {currentYear} PicnicBliss. All rights reserved.</p>
            </div>
            
            <div className="footer-links-bottom">
              <a href="#">Privacy Policy</a>
              <a href="#">Terms of Service</a>
              <a href="#">Cookie Policy</a>
            </div>
            
            <div className="made-with-love">
              <span>Made with <FaHeart className="heart-icon" /> in London</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
    </>
  );
};

export default Footer;
