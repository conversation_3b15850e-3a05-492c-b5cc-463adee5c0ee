import React from 'react';
import './PicnicTiers.css';
import bg from '../../images/bg1.svg';
const PicnicTiers: React.FC = () => {
  return (
    <section
      className="ppp-picnic-tiers-section-revised"
      style={{
        position: 'relative',
        backgroundImage: `url(${bg})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }}
    >
      <div className="ppp-container-revised">
        <h2 className="ppp-headline-revised">Choose Your Experience Tier</h2>
        <p className="ppp-subheadline-revised">
          What's Included in Your Picnic?
        </p>

        <div className="ppp-tiers-grid-revised">
          {/* Basic Setup */}
          <div className="ppp-tier-card-revised">
            <div className="ppp-tier-header-revised">
              <span className="ppp-tier-icon-revised" role="img" aria-label="chair">🪑</span>
              <h3 className="ppp-tier-title-revised">Basic Setup</h3>
            </div>
            <p className="ppp-tier-description-revised">
              Table, benches, plates, utensils, napkins
            </p>
          </div>

          {/* Premium Package */}
          <div className="ppp-tier-card-revised">
            <div className="ppp-tier-header-revised">
              <span className="ppp-tier-icon-revised" role="img" aria-label="camera">📸</span>
              <h3 className="ppp-tier-title-revised">Premium Package</h3>
            </div>
            <p className="ppp-tier-description-revised">
              Scenic location, luxury decor, photographer
            </p>
          </div>

          {/* Catering Add-on */}
          <div className="ppp-tier-card-revised">
            <div className="ppp-tier-header-revised">
              <span className="ppp-tier-icon-revised" role="img" aria-label="plate and cutlery">🍽️</span>
              <h3 className="ppp-tier-title-revised">Catering Add-on</h3>
            </div>
            <p className="ppp-tier-description-revised">
              Full meal service: sandwiches, drinks, dessert
            </p>
          </div>

          {/* Live Music */}
          <div className="ppp-tier-card-revised">
            <div className="ppp-tier-header-revised">
              <span className="ppp-tier-icon-revised" role="img" aria-label="musical notes">🎶</span>
              <h3 className="ppp-tier-title-revised">Live Music</h3>
            </div>
            <p className="ppp-tier-description-revised">
              Acoustic guitarist or violinist on location
            </p>
          </div>
        </div>

        <div className="ppp-cta-container-revised">
          <a href="/packages" className="ppp-cta-button-revised">
            🔍 View Packages →
          </a>
        </div>
      </div>
    </section>
  );
};

export default PicnicTiers;
