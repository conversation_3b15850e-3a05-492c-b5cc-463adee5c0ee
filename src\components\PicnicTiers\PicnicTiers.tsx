import React from 'react';
import './PicnicTiers.css';

// Professional background SVG component for PicnicTiers
const PicnicTiersBackgroundSVG: React.FC = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="100%"
    height="100%"
    version="1.1"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    transform="matrix(-1,1.2246467991473532e-16,-1.2246467991473532e-16,-1,0,0)"
    style={{
      position: 'absolute',
      top: 0,
      left: 0,
      zIndex: -1,
      width: '100%',
      height: '100%'
    }}
  >
    <defs>
      <linearGradient id="picnic-gradient-a" gradientUnits="userSpaceOnUse" x1="0" x2="0" y1="0" y2="100%" gradientTransform="rotate(240)">
        <stop offset="0" stopColor="#ffffff"/>
        <stop offset="1" stopColor="#92FFFE"/>
      </linearGradient>
      <pattern patternUnits="userSpaceOnUse" id="picnic-pattern-b" width="540" height="450" x="0" y="0" viewBox="0 0 1080 900">
        <g fillOpacity="0.1">
          <polygon fill="#444" points="90,150 0,300 180,300"/>
          <polygon points="90,150 180,0 0,0"/>
          <polygon fill="#AAA" points="270,150 360,0 180,0"/>
          <polygon fill="#DDD" points="450,150 360,300 540,300"/>
          <polygon fill="#999" points="450,150 540,0 360,0"/>
          <polygon points="630,150 540,300 720,300"/>
          <polygon fill="#DDD" points="630,150 720,0 540,0"/>
          <polygon fill="#444" points="810,150 720,300 900,300"/>
          <polygon fill="#FFF" points="810,150 900,0 720,0"/>
          <polygon fill="#DDD" points="990,150 900,300 1080,300"/>
          <polygon fill="#444" points="990,150 1080,0 900,0"/>
          <polygon fill="#DDD" points="90,450 0,600 180,600"/>
          <polygon points="90,450 180,300 0,300"/>
          <polygon fill="#666" points="270,450 180,600 360,600"/>
          <polygon fill="#AAA" points="270,450 360,300 180,300"/>
          <polygon fill="#DDD" points="450,450 360,600 540,600"/>
          <polygon fill="#999" points="450,450 540,300 360,300"/>
          <polygon fill="#999" points="630,450 540,600 720,600"/>
          <polygon fill="#FFF" points="630,450 720,300 540,300"/>
          <polygon points="810,450 720,600 900,600"/>
          <polygon fill="#DDD" points="810,450 900,300 720,300"/>
          <polygon fill="#AAA" points="990,450 900,600 1080,600"/>
          <polygon fill="#444" points="990,450 1080,300 900,300"/>
          <polygon fill="#222" points="90,750 0,900 180,900"/>
          <polygon points="270,750 180,900 360,900"/>
          <polygon fill="#DDD" points="270,750 360,600 180,600"/>
          <polygon points="450,750 360,900 540,900"/>
          <polygon points="450,750 540,600 360,600"/>
          <polygon points="630,750 540,900 720,900"/>
          <polygon fill="#444" points="630,750 720,600 540,600"/>
          <polygon fill="#AAA" points="810,750 720,900 900,900"/>
          <polygon fill="#666" points="810,750 900,600 720,600"/>
          <polygon fill="#999" points="990,750 900,900 1080,900"/>
          <polygon fill="#999" points="180,0 90,150 270,150"/>
          <polygon fill="#444" points="360,0 270,150 450,150"/>
          <polygon fill="#FFF" points="540,0 450,150 630,150"/>
          <polygon points="900,0 810,150 990,150"/>
          <polygon fill="#222" points="0,300 90,450 270,450"/>
          <polygon fill="#FFF" points="180,300 270,450 450,450"/>
          <polygon fill="#FFF" points="360,300 450,450 630,450"/>
          <polygon fill="#222" points="540,300 630,450 810,450"/>
          <polygon fill="#222" points="720,300 810,450 990,450"/>
          <polygon fill="#FFF" points="900,300 990,450 1080,450"/>
          <polygon points="0,600 90,750 270,750"/>
          <polygon fill="#666" points="180,600 270,750 450,750"/>
          <polygon fill="#AAA" points="360,600 450,750 630,750"/>
          <polygon fill="#444" points="540,600 630,750 810,750"/>
          <polygon fill="#222" points="720,600 810,750 990,750"/>
          <polygon fill="#FFF" points="900,600 990,750 1080,750"/>
        </g>
      </pattern>
    </defs>
    <rect x="0" y="0" fill="url(#picnic-gradient-a)" width="100%" height="100%"/>
    <rect x="0" y="0" fill="url(#picnic-pattern-b)" width="100%" height="100%"/>
  </svg>
);

const PicnicTiers: React.FC = () => {
  return (
    <section className="ppp-picnic-tiers-section-revised" style={{ position: 'relative' }}>
      <PicnicTiersBackgroundSVG />
      <div className="ppp-container-revised">
        <h2 className="ppp-headline-revised">Choose Your Experience Tier</h2>
        <p className="ppp-subheadline-revised">
          What's Included in Your Picnic?
        </p>

        <div className="ppp-tiers-grid-revised">
          {/* Basic Setup */}
          <div className="ppp-tier-card-revised">
            <div className="ppp-tier-header-revised">
              <span className="ppp-tier-icon-revised" role="img" aria-label="chair">🪑</span>
              <h3 className="ppp-tier-title-revised">Basic Setup</h3>
            </div>
            <p className="ppp-tier-description-revised">
              Table, benches, plates, utensils, napkins
            </p>
          </div>

          {/* Premium Package */}
          <div className="ppp-tier-card-revised">
            <div className="ppp-tier-header-revised">
              <span className="ppp-tier-icon-revised" role="img" aria-label="camera">📸</span>
              <h3 className="ppp-tier-title-revised">Premium Package</h3>
            </div>
            <p className="ppp-tier-description-revised">
              Scenic location, luxury decor, photographer
            </p>
          </div>

          {/* Catering Add-on */}
          <div className="ppp-tier-card-revised">
            <div className="ppp-tier-header-revised">
              <span className="ppp-tier-icon-revised" role="img" aria-label="plate and cutlery">🍽️</span>
              <h3 className="ppp-tier-title-revised">Catering Add-on</h3>
            </div>
            <p className="ppp-tier-description-revised">
              Full meal service: sandwiches, drinks, dessert
            </p>
          </div>

          {/* Live Music */}
          <div className="ppp-tier-card-revised">
            <div className="ppp-tier-header-revised">
              <span className="ppp-tier-icon-revised" role="img" aria-label="musical notes">🎶</span>
              <h3 className="ppp-tier-title-revised">Live Music</h3>
            </div>
            <p className="ppp-tier-description-revised">
              Acoustic guitarist or violinist on location
            </p>
          </div>
        </div>

        <div className="ppp-cta-container-revised">
          <a href="/packages" className="ppp-cta-button-revised">
            🔍 View Packages →
          </a>
        </div>
      </div>
    </section>
  );
};

export default PicnicTiers;
