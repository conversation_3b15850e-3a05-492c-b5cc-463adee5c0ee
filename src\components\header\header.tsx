// src/components/Header/Header.tsx
import React, { useState, useEffect } from 'react';
import { 
  FaPhoneAlt, FaEnvelope, FaFacebookF, FaTwitter, FaInstagram, 
  FaCalendarCheck, FaTimes, FaBars
} from 'react-icons/fa';
import './header.css';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Prevent scrolling when mobile menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isMenuOpen]);

  return (
    <>
      {/* Top Bar */}
      <div className={`top-bar ${isScrolled ? 'scrolled' : ''}`}>
        <div className="top-bar-content">
          <div className="contact-info">
            <div className="contact-item">
              <FaPhoneAlt className="contact-icon" />
              <span>+****************</span>
            </div>
            <div className="contact-item">
              <FaEnvelope className="contact-icon" />
              <span><EMAIL></span>
            </div>
          </div>
          
          <div className="social-links">
            <a href="https://facebook.com/picnicdate" target="_blank" rel="noopener noreferrer" title="Facebook">
              <FaFacebookF className="social-icon" />
            </a>
            <a href="https://twitter.com/picnicdate" target="_blank" rel="noopener noreferrer" title="Twitter">
              <FaTwitter className="social-icon" />
            </a>
            <a href="https://instagram.com/picnicdate" target="_blank" rel="noopener noreferrer" title="Instagram">
              <FaInstagram className="social-icon" />
            </a>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <header className={`main-header ${isScrolled ? 'scrolled' : ''}`}>
        <div className="header-container">
          <a href="/" className="logo">
            <div className="logo-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 17L12 21L20 17M4 17V11L12 15M20 17V11L12 15M12 15V3M4 11L12 7L20 11" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M7 8L12 5.5L17 8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <span>PicnicDate</span>
          </a>
          
          <nav className="desktop-nav">
            <ul>
              <li><a href="/">Home</a></li>
              <li><a href="/about">About</a></li>
              <li><a href="/packages">Packages</a></li>
              <li><a href="/gallery">Gallery</a></li>
              <li><a href="/contact">Contact</a></li>
            </ul>
          </nav>
          
          <button className="book-btn">
            <FaCalendarCheck className="btn-icon" />
            <span>Book a Picnic</span>
          </button>
          
          <button 
            className="mobile-menu-btn" 
            onClick={toggleMenu}
            aria-label={isMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMenuOpen ? <FaTimes /> : <FaBars />}
          </button>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      <div className={`mobile-menu-overlay ${isMenuOpen ? 'open' : ''}`} onClick={toggleMenu}></div>
      
      {/* Mobile Menu */}
      <div className={`mobile-menu ${isMenuOpen ? 'open' : ''}`}>
        <div className="mobile-menu-header">
          <div className="mobile-logo">
            <div className="logo-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 17L12 21L20 17M4 17V11L12 15M20 17V11L12 15M12 15V3M4 11L12 7L20 11" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M7 8L12 5.5L17 8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <span>PicnicDate</span>
          </div>
          <button className="mobile-menu-close" onClick={toggleMenu}>
            <FaTimes />
          </button>
        </div>
        
        <nav className="mobile-nav">
          <ul>
            <li><a href="/" onClick={toggleMenu}>Home</a></li>
            <li><a href="/about" onClick={toggleMenu}>About</a></li>
            <li><a href="/packages" onClick={toggleMenu}>Packages</a></li>
            <li><a href="/gallery" onClick={toggleMenu}>Gallery</a></li>
            <li><a href="/contact" onClick={toggleMenu}>Contact</a></li>
          </ul>
        </nav>
        
        <div className="mobile-cta">
          <button className="mobile-book-btn" onClick={toggleMenu}>
            <FaCalendarCheck className="btn-icon" />
            <span>Book a Picnic</span>
          </button>
        </div>
        
        <div className="mobile-contact">
          <div className="contact-item">
            <FaPhoneAlt className="contact-icon" />
            <span>+****************</span>
          </div>
          <div className="contact-item">
            <FaEnvelope className="contact-icon" />
            <span><EMAIL></span>
          </div>
          
          <div className="mobile-social-links">
            <a href="https://facebook.com/picnicdate" target="_blank" rel="noopener noreferrer" title="Facebook">
              <FaFacebookF className="social-icon" />
            </a>
            <a href="https://twitter.com/picnicdate" target="_blank" rel="noopener noreferrer" title="Twitter">
              <FaTwitter className="social-icon" />
            </a>
            <a href="https://instagram.com/picnicdate" target="_blank" rel="noopener noreferrer" title="Instagram">
              <FaInstagram className="social-icon" />
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

export default Header;